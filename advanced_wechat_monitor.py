#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级微信消息监测程序
使用Windows消息钩子和UI自动化技术
"""

import time
import win32gui
import win32con
import win32api
import win32process
import psutil
from datetime import datetime
import threading
import queue
import json
import os

class AdvancedWeChatMonitor:
    def __init__(self):
        """初始化高级监测器"""
        self.wechat_hwnd = None
        self.wechat_pid = None
        self.is_monitoring = False
        self.message_queue = queue.Queue()
        self.last_message_time = 0
        self.config_file = "monitor_config.json"
        self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        default_config = {
            "check_interval": 2,  # 检查间隔（秒）
            "log_to_file": True,  # 是否记录到文件
            "log_file": "wechat_messages.log",
            "show_timestamp": True,
            "show_window_changes": True,
            "filter_keywords": []  # 过滤关键词
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = default_config
                self.save_config()
        except:
            self.config = default_config
            
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            
    def find_wechat_process(self):
        """查找微信进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    if proc.info['name'] and 'WeChat' in proc.info['name']:
                        self.wechat_pid = proc.info['pid']
                        print(f"找到微信进程: PID={self.wechat_pid}")
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            print(f"查找微信进程时出错: {e}")
        return False
        
    def find_wechat_window(self):
        """查找微信窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                try:
                    class_name = win32gui.GetClassName(hwnd)
                    window_text = win32gui.GetWindowText(hwnd)
                    
                    # 获取窗口所属进程ID
                    _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                    
                    # 检查是否是微信窗口
                    if (("WeChatMainWndForPC" in class_name or 
                         "微信" in window_text or 
                         "WeChat" in window_text) and
                        (self.wechat_pid is None or window_pid == self.wechat_pid)):
                        windows.append((hwnd, window_text, class_name, window_pid))
                except:
                    pass
                    
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            self.wechat_hwnd, title, class_name, pid = windows[0]
            self.wechat_pid = pid
            print(f"找到微信窗口: {title}")
            print(f"窗口句柄: {self.wechat_hwnd}, 进程ID: {self.wechat_pid}")
            return True
        else:
            print("未找到微信窗口")
            return False
            
    def get_window_info(self):
        """获取窗口详细信息"""
        try:
            if not self.wechat_hwnd or not win32gui.IsWindow(self.wechat_hwnd):
                return None
                
            title = win32gui.GetWindowText(self.wechat_hwnd)
            class_name = win32gui.GetClassName(self.wechat_hwnd)
            rect = win32gui.GetWindowRect(self.wechat_hwnd)
            is_visible = win32gui.IsWindowVisible(self.wechat_hwnd)
            is_foreground = win32gui.GetForegroundWindow() == self.wechat_hwnd
            
            return {
                'title': title,
                'class_name': class_name,
                'rect': rect,
                'is_visible': is_visible,
                'is_foreground': is_foreground,
                'hwnd': self.wechat_hwnd
            }
        except Exception as e:
            print(f"获取窗口信息时出错: {e}")
            return None
            
    def log_message(self, message):
        """记录消息到文件"""
        if self.config.get('log_to_file', False):
            try:
                log_file = self.config.get('log_file', 'wechat_messages.log')
                with open(log_file, 'a', encoding='utf-8') as f:
                    f.write(f"{message}\n")
            except Exception as e:
                print(f"写入日志文件失败: {e}")
                
    def print_and_log(self, message):
        """打印并记录消息"""
        print(message)
        self.log_message(message)
        
    def check_for_notifications(self):
        """检查系统通知（Windows 10/11）"""
        try:
            # 这里可以添加检查Windows通知的代码
            # 由于权限限制，这个功能可能需要额外的设置
            pass
        except Exception as e:
            print(f"检查通知时出错: {e}")
            
    def monitor_wechat_activity(self):
        """监测微信活动"""
        print("开始监测微信活动...")
        print(f"检查间隔: {self.config['check_interval']}秒")
        print("按 Ctrl+C 停止监测")
        print("-" * 60)
        
        last_window_info = None
        
        while self.is_monitoring:
            try:
                # 检查微信进程是否还在运行
                if self.wechat_pid:
                    try:
                        proc = psutil.Process(self.wechat_pid)
                        if not proc.is_running():
                            print("微信进程已结束，重新查找...")
                            self.wechat_hwnd = None
                            self.wechat_pid = None
                    except psutil.NoSuchProcess:
                        print("微信进程已结束，重新查找...")
                        self.wechat_hwnd = None
                        self.wechat_pid = None
                        
                # 如果没有窗口句柄，尝试查找
                if not self.wechat_hwnd:
                    if not self.find_wechat_process():
                        print("未找到微信进程，5秒后重试...")
                        time.sleep(5)
                        continue
                        
                    if not self.find_wechat_window():
                        print("未找到微信窗口，5秒后重试...")
                        time.sleep(5)
                        continue
                        
                # 获取当前窗口信息
                current_window_info = self.get_window_info()
                
                if current_window_info:
                    # 检查窗口信息是否发生变化
                    if last_window_info is None or current_window_info != last_window_info:
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        
                        # 分析变化类型
                        if last_window_info is None:
                            self.print_and_log(f"[{timestamp}] 开始监测微信窗口: {current_window_info['title']}")
                        else:
                            changes = []
                            
                            if current_window_info['title'] != last_window_info['title']:
                                changes.append(f"标题变化: '{last_window_info['title']}' -> '{current_window_info['title']}'")
                                
                            if current_window_info['is_foreground'] != last_window_info['is_foreground']:
                                if current_window_info['is_foreground']:
                                    changes.append("窗口获得焦点")
                                else:
                                    changes.append("窗口失去焦点")
                                    
                            if current_window_info['is_visible'] != last_window_info['is_visible']:
                                if current_window_info['is_visible']:
                                    changes.append("窗口变为可见")
                                else:
                                    changes.append("窗口变为隐藏")
                                    
                            if changes and self.config.get('show_window_changes', True):
                                for change in changes:
                                    self.print_and_log(f"[{timestamp}] {change}")
                                    
                        last_window_info = current_window_info.copy()
                        
                # 检查系统通知
                self.check_for_notifications()
                
                # 等待指定的时间间隔
                time.sleep(self.config['check_interval'])
                
            except KeyboardInterrupt:
                print("\n监测已停止")
                break
            except Exception as e:
                print(f"监测过程中出错: {e}")
                time.sleep(5)
                
    def start_monitoring(self):
        """开始监测"""
        if self.is_monitoring:
            print("监测已在运行中...")
            return
            
        self.is_monitoring = True
        
        try:
            self.monitor_wechat_activity()
        except KeyboardInterrupt:
            print("\n监测已停止")
        finally:
            self.is_monitoring = False
            
    def stop_monitoring(self):
        """停止监测"""
        self.is_monitoring = False
        
    def show_config(self):
        """显示当前配置"""
        print("当前配置:")
        print(json.dumps(self.config, ensure_ascii=False, indent=2))

def main():
    """主函数"""
    print("高级微信消息监测程序")
    print("=" * 60)
    print("功能特性：")
    print("- 监测微信进程和窗口状态")
    print("- 检测窗口标题变化和焦点变化")
    print("- 支持配置文件自定义设置")
    print("- 可选的日志文件记录")
    print("- 进程状态监控")
    print()
    
    # 创建监测器实例
    monitor = AdvancedWeChatMonitor()
    
    # 显示配置
    monitor.show_config()
    print()
    
    try:
        # 开始监测
        monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
