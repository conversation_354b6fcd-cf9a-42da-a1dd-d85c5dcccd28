微信消息监测程序使用说明
==========================

📋 程序功能
-----------
✅ 实时监测微信窗口标题变化
✅ 检测新消息并显示发送者信息  
✅ 自动记录消息日志到文件
✅ 进程状态监控和自动恢复
✅ 支持表情符号和时间戳显示
✅ 轻量级设计，不干扰微信正常使用

🚀 快速开始
-----------
1. 确保微信已经打开并登录
2. 运行命令：python simple_wechat_monitor.py
3. 程序会自动检测微信窗口和进程
4. 当有新消息时会在终端显示
5. 按 Ctrl+C 停止监测

📁 文件说明
-----------
• simple_wechat_monitor.py - 主程序文件
• requirements.txt - 依赖包列表
• wechat_messages.log - 消息日志文件（自动生成）
• start_monitor.bat - Windows启动脚本
• 使用说明.txt - 本说明文件

⚙️ 系统要求
-----------
• Windows 10/11
• Python 3.7+
• 微信PC版

📦 依赖安装
-----------
pip install pywin32==306 psutil==5.9.5

🔧 程序配置
-----------
程序内置配置项（可在代码中修改）：
• check_interval: 1.5秒 - 检查间隔
• log_to_file: True - 是否记录到文件
• show_process_info: True - 显示进程信息
• auto_restart: True - 自动重启监测
• sound_alert: False - 声音提醒

📊 监测信息
-----------
程序会显示以下信息：
• 🔔 新消息提醒（显示发送者和消息数量）
• 💬 聊天窗口切换
• 📱 窗口状态变化
• ⚡ 进程状态监控

📝 日志格式
-----------
[2025-07-31 18:06:32] 🔔 来自 '张三' 的新消息 (2条) [总计: 5]
[2025-07-31 18:06:45] 💬 切换到聊天: 工作群
[2025-07-31 18:07:01] 📱 窗口标题变化: 微信(3)

🛠️ 故障排除
-----------
1. 如果提示"未找到微信窗口"：
   • 确保微信已打开并登录
   • 尝试重新启动微信
   • 检查微信是否被最小化

2. 如果无法检测到新消息：
   • 确保微信窗口可见
   • 检查微信版本兼容性
   • 尝试重新启动程序

3. 如果程序频繁重启：
   • 检查微信进程是否稳定
   • 确保没有其他程序干扰
   • 以管理员权限运行

4. 如果依赖包安装失败：
   • 升级pip: python -m pip install --upgrade pip
   • 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/

⚠️ 注意事项
-----------
• 程序需要访问Windows API，可能被杀毒软件拦截
• 监测期间建议保持微信窗口可见
• 程序会自动处理微信重启和窗口变化
• 日志文件会持续增长，建议定期清理

🔄 热加载功能
-----------
程序支持自动恢复功能：
• 微信进程重启时自动重新连接
• 窗口关闭时自动重新查找
• 连续错误时自动停止保护

💡 使用技巧
-----------
• 可以同时运行多个监测实例
• 日志文件支持UTF-8编码，可用记事本查看
• 程序运行时不影响微信正常使用
• 支持后台运行，最小化不影响监测

📞 技术支持
-----------
如遇问题，请检查：
1. Python版本是否正确
2. 依赖包是否完整安装
3. 微信版本是否支持
4. 系统权限是否充足

程序版本：v1.0
更新日期：2025-07-31
