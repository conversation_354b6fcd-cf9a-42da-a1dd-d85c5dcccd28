#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信消息监测程序
监测微信窗口的新消息并在终端打印出来
"""

import time
import pygetwindow as gw
import pyautogui
import win32gui
import win32con
import win32clipboard
from datetime import datetime
import re
import threading
import queue

class WeChatMonitor:
    def __init__(self):
        """初始化微信监测器"""
        self.wechat_window = None
        self.last_message_count = 0
        self.message_queue = queue.Queue()
        self.is_monitoring = False
        self.last_chat_content = ""
        
        # 禁用pyautogui的安全机制
        pyautogui.FAILSAFE = False
        
    def find_wechat_window(self):
        """查找微信窗口"""
        try:
            # 尝试多种可能的微信窗口标题
            wechat_titles = ["微信", "WeChat", "微信 ", "WeChat "]
            
            for title in wechat_titles:
                windows = gw.getWindowsWithTitle(title)
                if windows:
                    self.wechat_window = windows[0]
                    print(f"找到微信窗口: {self.wechat_window.title}")
                    return True
                    
            # 如果没找到，尝试通过类名查找
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    class_name = win32gui.GetClassName(hwnd)
                    window_text = win32gui.GetWindowText(hwnd)
                    if "WeChatMainWndForPC" in class_name or "微信" in window_text:
                        windows.append((hwnd, window_text))
                        
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            if windows:
                hwnd, title = windows[0]
                # 创建一个模拟的窗口对象
                class MockWindow:
                    def __init__(self, hwnd, title):
                        self.hwnd = hwnd
                        self.title = title
                        
                    def activate(self):
                        win32gui.SetForegroundWindow(self.hwnd)
                        
                    def isActive(self):
                        return win32gui.GetForegroundWindow() == self.hwnd
                        
                self.wechat_window = MockWindow(hwnd, title)
                print(f"通过类名找到微信窗口: {title}")
                return True
                
        except Exception as e:
            print(f"查找微信窗口时出错: {e}")
            
        return False
        
    def get_clipboard_text(self):
        """获取剪贴板文本"""
        try:
            win32clipboard.OpenClipboard()
            data = win32clipboard.GetClipboardData(win32con.CF_UNICODETEXT)
            win32clipboard.CloseClipboard()
            return data
        except:
            try:
                win32clipboard.CloseClipboard()
            except:
                pass
            return ""
            
    def set_clipboard_text(self, text):
        """设置剪贴板文本"""
        try:
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardData(win32con.CF_UNICODETEXT, text)
            win32clipboard.CloseClipboard()
        except:
            try:
                win32clipboard.CloseClipboard()
            except:
                pass
                
    def capture_chat_content(self):
        """捕获聊天内容"""
        try:
            if not self.wechat_window:
                return ""

            # 检查微信窗口是否还存在
            try:
                if hasattr(self.wechat_window, 'hwnd'):
                    if not win32gui.IsWindow(self.wechat_window.hwnd):
                        print("微信窗口已关闭，重新查找...")
                        self.wechat_window = None
                        return ""
                else:
                    # 对于pygetwindow对象，检查是否还存在
                    if not gw.getWindowsWithTitle(self.wechat_window.title):
                        print("微信窗口已关闭，重新查找...")
                        self.wechat_window = None
                        return ""
            except:
                self.wechat_window = None
                return ""

            # 激活微信窗口
            self.wechat_window.activate()
            time.sleep(0.2)

            # 保存当前剪贴板内容
            original_clipboard = self.get_clipboard_text()

            # 点击聊天区域确保焦点正确
            # 这里可以根据需要调整坐标
            try:
                # 获取窗口位置和大小，点击聊天区域中央
                if hasattr(self.wechat_window, 'hwnd'):
                    rect = win32gui.GetWindowRect(self.wechat_window.hwnd)
                    x = rect[0] + (rect[2] - rect[0]) // 2
                    y = rect[1] + (rect[3] - rect[1]) // 2
                    pyautogui.click(x, y)
                    time.sleep(0.1)
            except:
                pass

            # 全选聊天内容 (Ctrl+A)
            pyautogui.hotkey('ctrl', 'a')
            time.sleep(0.2)

            # 复制内容 (Ctrl+C)
            pyautogui.hotkey('ctrl', 'c')
            time.sleep(0.2)

            # 获取复制的内容
            chat_content = self.get_clipboard_text()

            # 恢复原始剪贴板内容
            self.set_clipboard_text(original_clipboard)

            # 按ESC取消选择
            pyautogui.press('esc')
            time.sleep(0.1)

            return chat_content

        except Exception as e:
            print(f"捕获聊天内容时出错: {e}")
            return ""
            
    def extract_new_messages(self, current_content):
        """提取新消息"""
        try:
            if not self.last_chat_content:
                self.last_chat_content = current_content
                return []
                
            # 如果内容没有变化，返回空列表
            if current_content == self.last_chat_content:
                return []
                
            # 简单的新消息检测：比较内容长度和末尾内容
            if len(current_content) > len(self.last_chat_content):
                # 获取新增的内容
                new_content = current_content[len(self.last_chat_content):]
                
                # 更新最后的聊天内容
                self.last_chat_content = current_content
                
                # 分割成行并过滤空行
                new_lines = [line.strip() for line in new_content.split('\n') if line.strip()]
                
                return new_lines
            else:
                # 内容可能被重新排列，更新记录
                self.last_chat_content = current_content
                return []
                
        except Exception as e:
            print(f"提取新消息时出错: {e}")
            return []
            
    def monitor_messages(self):
        """监测消息的主循环"""
        print("开始监测微信消息...")
        print("按 Ctrl+C 停止监测")
        print("-" * 50)
        
        while self.is_monitoring:
            try:
                if not self.wechat_window:
                    if not self.find_wechat_window():
                        print("未找到微信窗口，5秒后重试...")
                        time.sleep(5)
                        continue
                        
                # 捕获当前聊天内容
                current_content = self.capture_chat_content()
                
                if current_content:
                    # 提取新消息
                    new_messages = self.extract_new_messages(current_content)
                    
                    # 打印新消息
                    for message in new_messages:
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        print(f"[{timestamp}] 新消息: {message}")
                        
                # 等待一段时间再次检查
                time.sleep(2)
                
            except KeyboardInterrupt:
                print("\n监测已停止")
                break
            except Exception as e:
                print(f"监测过程中出错: {e}")
                time.sleep(5)
                
    def start_monitoring(self):
        """开始监测"""
        if self.is_monitoring:
            print("监测已在运行中...")
            return
            
        self.is_monitoring = True
        
        # 查找微信窗口
        if not self.find_wechat_window():
            print("错误: 未找到微信窗口，请确保微信已打开")
            return
            
        # 开始监测
        try:
            self.monitor_messages()
        except KeyboardInterrupt:
            print("\n监测已停止")
        finally:
            self.is_monitoring = False
            
    def stop_monitoring(self):
        """停止监测"""
        self.is_monitoring = False

def main():
    """主函数"""
    print("微信消息监测程序")
    print("=" * 50)
    
    # 创建监测器实例
    monitor = WeChatMonitor()
    
    try:
        # 开始监测
        monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
