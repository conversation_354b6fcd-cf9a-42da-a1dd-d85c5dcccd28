微信消息监测程序使用说明
================================

功能介绍：
- 实时监测微信窗口的新消息
- 在终端中打印出新收到的消息
- 支持中文消息显示
- 带时间戳显示

使用步骤：

1. 安装依赖包
   双击运行 install_dependencies.bat
   或者在命令行中运行：pip install -r requirements.txt

2. 准备工作
   - 打开微信并登录
   - 打开要监测的聊天窗口（个人聊天或群聊）
   - 确保聊天窗口处于活动状态

3. 启动监测
   方法一：双击运行 run_monitor.bat
   方法二：在命令行中运行：python wechat_monitor.py

4. 停止监测
   按 Ctrl+C 停止程序

注意事项：
- 程序需要访问剪贴板和控制鼠标键盘，可能被杀毒软件拦截
- 监测期间不要频繁切换窗口，可能影响监测效果
- 程序会每2秒检查一次新消息
- 确保微信窗口标题包含"微信"或"WeChat"

技术原理：
- 使用pygetwindow查找微信窗口
- 使用pyautogui模拟键盘操作（Ctrl+A, Ctrl+C）
- 通过剪贴板获取聊天内容
- 比较内容变化来检测新消息

故障排除：
1. 如果提示"未找到微信窗口"：
   - 确保微信已打开
   - 尝试重新启动微信
   - 检查微信窗口标题

2. 如果无法检测到新消息：
   - 确保聊天窗口处于活动状态
   - 尝试手动点击聊天输入框
   - 检查是否有其他程序占用剪贴板

3. 如果程序崩溃：
   - 检查Python版本（建议3.7+）
   - 重新安装依赖包
   - 以管理员权限运行

系统要求：
- Windows 10/11
- Python 3.7+
- 微信PC版
