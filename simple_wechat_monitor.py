#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版微信消息监测程序
通过监测微信窗口标题变化来检测新消息
"""

import time
import win32gui
import win32con
from datetime import datetime
import re

class SimpleWeChatMonitor:
    def __init__(self):
        """初始化监测器"""
        self.wechat_hwnd = None
        self.last_title = ""
        self.is_monitoring = False
        
    def find_wechat_window(self):
        """查找微信窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                class_name = win32gui.GetClassName(hwnd)
                window_text = win32gui.GetWindowText(hwnd)
                
                # 检查是否是微信主窗口
                if ("WeChatMainWndForPC" in class_name or 
                    "微信" in window_text or 
                    "WeChat" in window_text):
                    windows.append((hwnd, window_text, class_name))
                    
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            self.wechat_hwnd, title, class_name = windows[0]
            print(f"找到微信窗口: {title} (类名: {class_name})")
            self.last_title = title
            return True
        else:
            print("未找到微信窗口")
            return False
            
    def get_window_title(self):
        """获取微信窗口标题"""
        try:
            if self.wechat_hwnd and win32gui.IsWindow(self.wechat_hwnd):
                return win32gui.GetWindowText(self.wechat_hwnd)
            else:
                return ""
        except:
            return ""
            
    def extract_message_info(self, title):
        """从窗口标题提取消息信息"""
        # 微信窗口标题格式通常为：
        # "微信" - 无新消息
        # "微信(1)" - 有1条新消息
        # "联系人名称 - 微信" - 当前聊天窗口
        # "联系人名称(数字) - 微信" - 当前聊天窗口有新消息
        
        message_info = {
            'has_new_message': False,
            'message_count': 0,
            'contact_name': '',
            'title': title
        }
        
        try:
            # 检查是否有消息计数
            count_match = re.search(r'\((\d+)\)', title)
            if count_match:
                message_info['has_new_message'] = True
                message_info['message_count'] = int(count_match.group(1))
                
            # 提取联系人名称
            if ' - 微信' in title:
                contact_part = title.split(' - 微信')[0]
                # 移除消息计数部分
                contact_name = re.sub(r'\(\d+\)$', '', contact_part).strip()
                message_info['contact_name'] = contact_name
                
        except Exception as e:
            print(f"解析标题时出错: {e}")
            
        return message_info
        
    def monitor_title_changes(self):
        """监测窗口标题变化"""
        print("开始监测微信窗口标题变化...")
        print("当有新消息时会在终端显示")
        print("按 Ctrl+C 停止监测")
        print("-" * 50)
        
        while self.is_monitoring:
            try:
                # 检查微信窗口是否还存在
                if not self.wechat_hwnd or not win32gui.IsWindow(self.wechat_hwnd):
                    print("微信窗口已关闭，重新查找...")
                    if not self.find_wechat_window():
                        print("未找到微信窗口，5秒后重试...")
                        time.sleep(5)
                        continue
                        
                # 获取当前窗口标题
                current_title = self.get_window_title()
                
                if current_title and current_title != self.last_title:
                    # 标题发生变化，分析消息信息
                    message_info = self.extract_message_info(current_title)
                    
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    
                    if message_info['has_new_message']:
                        if message_info['contact_name']:
                            print(f"[{timestamp}] 来自 '{message_info['contact_name']}' 的新消息 ({message_info['message_count']}条)")
                        else:
                            print(f"[{timestamp}] 新消息 ({message_info['message_count']}条)")
                    else:
                        if message_info['contact_name']:
                            print(f"[{timestamp}] 切换到聊天: {message_info['contact_name']}")
                        else:
                            print(f"[{timestamp}] 窗口标题变化: {current_title}")
                    
                    # 更新最后的标题
                    self.last_title = current_title
                    
                # 等待一段时间再检查
                time.sleep(1)
                
            except KeyboardInterrupt:
                print("\n监测已停止")
                break
            except Exception as e:
                print(f"监测过程中出错: {e}")
                time.sleep(3)
                
    def start_monitoring(self):
        """开始监测"""
        if self.is_monitoring:
            print("监测已在运行中...")
            return
            
        # 查找微信窗口
        if not self.find_wechat_window():
            print("错误: 未找到微信窗口，请确保微信已打开")
            return
            
        self.is_monitoring = True
        
        try:
            self.monitor_title_changes()
        except KeyboardInterrupt:
            print("\n监测已停止")
        finally:
            self.is_monitoring = False
            
    def stop_monitoring(self):
        """停止监测"""
        self.is_monitoring = False

def main():
    """主函数"""
    print("简化版微信消息监测程序")
    print("=" * 50)
    print("功能说明：")
    print("- 监测微信窗口标题变化")
    print("- 检测新消息提醒")
    print("- 显示联系人切换")
    print("- 轻量级，不干扰微信使用")
    print()
    
    # 创建监测器实例
    monitor = SimpleWeChatMonitor()
    
    try:
        # 开始监测
        monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
