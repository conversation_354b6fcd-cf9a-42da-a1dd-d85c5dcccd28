#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信消息监测程序
通过监测微信窗口标题变化来检测新消息，并支持多种监测模式
"""

import time
import win32gui
import win32con
import win32clipboard
import win32process
import psutil
from datetime import datetime
import re
import os
import json
import threading

class WeChatMonitor:
    def __init__(self):
        """初始化监测器"""
        self.wechat_hwnd = None
        self.wechat_pid = None
        self.last_title = ""
        self.is_monitoring = False
        self.message_count = 0
        self.log_file = "wechat_messages.log"
        self.config = {
            "check_interval": 1.5,  # 检查间隔（秒）
            "log_to_file": True,    # 是否记录到文件
            "show_process_info": True,  # 显示进程信息
            "auto_restart": True,   # 自动重启监测
            "sound_alert": False    # 声音提醒（可选）
        }

        # 创建日志文件
        if self.config["log_to_file"]:
            self.init_log_file()

    def init_log_file(self):
        """初始化日志文件"""
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(f"\n{'='*60}\n")
                f.write(f"微信监测开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"{'='*60}\n")
        except Exception as e:
            print(f"初始化日志文件失败: {e}")

    def log_message(self, message):
        """记录消息到文件和控制台"""
        print(message)
        if self.config["log_to_file"]:
            try:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(f"{message}\n")
            except Exception as e:
                print(f"写入日志失败: {e}")

    def find_wechat_process(self):
        """查找微信进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    if proc.info['name'] and 'WeChat' in proc.info['name']:
                        self.wechat_pid = proc.info['pid']
                        if self.config["show_process_info"]:
                            self.log_message(f"找到微信进程: PID={self.wechat_pid}, 名称={proc.info['name']}")
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            self.log_message(f"查找微信进程时出错: {e}")
        return False

    def find_wechat_window(self):
        """查找微信窗口"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                try:
                    class_name = win32gui.GetClassName(hwnd)
                    window_text = win32gui.GetWindowText(hwnd)

                    # 获取窗口所属进程ID
                    _, window_pid = win32process.GetWindowThreadProcessId(hwnd)

                    # 更精确的微信窗口识别
                    is_wechat_window = False

                    # 方法1: 通过类名识别（最可靠）
                    if "WeChatMainWndForPC" in class_name:
                        is_wechat_window = True

                    # 方法2: 通过进程ID匹配
                    elif self.wechat_pid and window_pid == self.wechat_pid:
                        # 确保不是子窗口或对话框，且窗口标题合理
                        if (window_text and
                            len(window_text.strip()) > 0 and
                            ("微信" in window_text or "WeChat" in window_text) and
                            "Visual Studio" not in window_text and
                            "Code" not in window_text and
                            "Chrome" not in class_name):
                            is_wechat_window = True

                    # 方法3: 通过窗口标题识别（作为备选）
                    elif (window_text and
                          ("微信" == window_text.strip() or
                           window_text.strip().startswith("微信(") or
                           " - 微信" in window_text) and
                          "Visual Studio" not in window_text and
                          "Code" not in window_text and
                          "Chrome" not in class_name):
                        is_wechat_window = True

                    if is_wechat_window:
                        windows.append((hwnd, window_text, class_name, window_pid))

                except Exception as e:
                    pass  # 忽略获取窗口信息时的错误
                    
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            # 优先选择类名为WeChatMainWndForPC的窗口
            wechat_main_windows = [w for w in windows if "WeChatMainWndForPC" in w[2]]
            if wechat_main_windows:
                self.wechat_hwnd, title, class_name, pid = wechat_main_windows[0]
            else:
                self.wechat_hwnd, title, class_name, pid = windows[0]

            self.wechat_pid = pid
            self.log_message(f"找到微信窗口: {title} (类名: {class_name}, PID: {pid})")
            self.last_title = title
            return True
        else:
            self.log_message("未找到微信窗口")
            return False
            
    def get_window_title(self):
        """获取微信窗口标题"""
        try:
            if self.wechat_hwnd and win32gui.IsWindow(self.wechat_hwnd):
                return win32gui.GetWindowText(self.wechat_hwnd)
            else:
                return ""
        except:
            return ""
            
    def extract_message_info(self, title):
        """从窗口标题提取消息信息"""
        # 微信窗口标题格式通常为：
        # "微信" - 无新消息
        # "微信(1)" - 有1条新消息
        # "联系人名称 - 微信" - 当前聊天窗口
        # "联系人名称(数字) - 微信" - 当前聊天窗口有新消息
        
        message_info = {
            'has_new_message': False,
            'message_count': 0,
            'contact_name': '',
            'title': title
        }
        
        try:
            # 检查是否有消息计数
            count_match = re.search(r'\((\d+)\)', title)
            if count_match:
                message_info['has_new_message'] = True
                message_info['message_count'] = int(count_match.group(1))
                
            # 提取联系人名称
            if ' - 微信' in title:
                contact_part = title.split(' - 微信')[0]
                # 移除消息计数部分
                contact_name = re.sub(r'\(\d+\)$', '', contact_part).strip()
                message_info['contact_name'] = contact_name
                
        except Exception as e:
            print(f"解析标题时出错: {e}")
            
        return message_info
        
    def check_process_status(self):
        """检查微信进程状态"""
        if self.wechat_pid:
            try:
                proc = psutil.Process(self.wechat_pid)
                return proc.is_running()
            except psutil.NoSuchProcess:
                return False
        return False

    def play_sound_alert(self):
        """播放声音提醒（可选功能）"""
        if self.config.get("sound_alert", False):
            try:
                import winsound
                winsound.Beep(1000, 200)  # 频率1000Hz，持续200ms
            except:
                pass  # 如果无法播放声音，忽略错误

    def monitor_title_changes(self):
        """监测窗口标题变化"""
        self.log_message("开始监测微信窗口标题变化...")
        self.log_message("当有新消息时会在终端显示")
        self.log_message("按 Ctrl+C 停止监测")
        self.log_message("-" * 60)

        consecutive_errors = 0
        max_errors = 5

        while self.is_monitoring:
            try:
                # 检查微信进程状态
                if not self.check_process_status():
                    self.log_message("微信进程已结束，重新查找...")
                    self.wechat_hwnd = None
                    self.wechat_pid = None
                    if not self.find_wechat_process():
                        self.log_message("未找到微信进程，5秒后重试...")
                        time.sleep(5)
                        continue

                # 检查微信窗口是否还存在
                if not self.wechat_hwnd or not win32gui.IsWindow(self.wechat_hwnd):
                    self.log_message("微信窗口已关闭，重新查找...")
                    if not self.find_wechat_window():
                        self.log_message("未找到微信窗口，5秒后重试...")
                        time.sleep(5)
                        continue

                # 获取当前窗口标题
                current_title = self.get_window_title()

                if current_title and current_title != self.last_title:
                    # 标题发生变化，分析消息信息
                    message_info = self.extract_message_info(current_title)

                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                    if message_info['has_new_message']:
                        self.message_count += message_info['message_count']

                        if message_info['contact_name']:
                            msg = f"[{timestamp}] 🔔 来自 '{message_info['contact_name']}' 的新消息 ({message_info['message_count']}条) [总计: {self.message_count}]"
                        else:
                            msg = f"[{timestamp}] 🔔 新消息 ({message_info['message_count']}条) [总计: {self.message_count}]"

                        self.log_message(msg)
                        self.play_sound_alert()  # 播放提醒音

                    else:
                        if message_info['contact_name']:
                            msg = f"[{timestamp}] 💬 切换到聊天: {message_info['contact_name']}"
                        else:
                            msg = f"[{timestamp}] 📱 窗口标题变化: {current_title}"
                        self.log_message(msg)

                    # 更新最后的标题
                    self.last_title = current_title

                # 重置错误计数
                consecutive_errors = 0

                # 等待指定的时间间隔
                time.sleep(self.config["check_interval"])

            except KeyboardInterrupt:
                self.log_message("\n监测已停止")
                break
            except Exception as e:
                consecutive_errors += 1
                error_msg = f"监测过程中出错 ({consecutive_errors}/{max_errors}): {e}"
                self.log_message(error_msg)

                if consecutive_errors >= max_errors:
                    self.log_message("连续错误过多，停止监测")
                    break

                time.sleep(3)
                
    def start_monitoring(self):
        """开始监测"""
        if self.is_monitoring:
            print("监测已在运行中...")
            return
            
        # 查找微信窗口
        if not self.find_wechat_window():
            print("错误: 未找到微信窗口，请确保微信已打开")
            return
            
        self.is_monitoring = True
        
        try:
            self.monitor_title_changes()
        except KeyboardInterrupt:
            print("\n监测已停止")
        finally:
            self.is_monitoring = False
            
    def stop_monitoring(self):
        """停止监测"""
        self.is_monitoring = False

def show_banner():
    """显示程序横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════╗
    ║                   微信消息监测程序                        ║
    ║                  WeChat Message Monitor                  ║
    ╠══════════════════════════════════════════════════════════╣
    ║  功能特性：                                              ║
    ║  • 实时监测微信窗口标题变化                              ║
    ║  • 检测新消息并显示发送者信息                            ║
    ║  • 自动记录消息日志到文件                                ║
    ║  • 进程状态监控和自动恢复                                ║
    ║  • 支持表情符号和时间戳显示                              ║
    ║  • 轻量级设计，不干扰微信正常使用                        ║
    ╚══════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """主函数"""
    show_banner()

    # 创建监测器实例
    monitor = WeChatMonitor()

    print("正在初始化监测器...")
    print(f"日志文件: {monitor.log_file}")
    print(f"检查间隔: {monitor.config['check_interval']}秒")
    print()

    try:
        # 开始监测
        monitor.start_monitoring()
    except KeyboardInterrupt:
        monitor.log_message("\n用户手动停止程序")
        print("程序已安全退出")
    except Exception as e:
        error_msg = f"程序运行出错: {e}"
        monitor.log_message(error_msg)
        print(error_msg)
    finally:
        # 记录程序结束时间
        if hasattr(monitor, 'log_message'):
            monitor.log_message(f"程序结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            monitor.log_message("=" * 60)

if __name__ == "__main__":
    main()
