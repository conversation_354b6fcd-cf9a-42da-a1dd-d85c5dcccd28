@echo off
echo 安装微信监测程序依赖包...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo Python已安装，开始安装依赖包...
echo.

REM 升级pip
python -m pip install --upgrade pip

REM 安装依赖包
pip install pygetwindow==0.0.9
pip install pyautogui==0.9.54
pip install pywin32==306
pip install Pillow==10.0.0

echo.
echo 依赖包安装完成！
echo 现在可以运行 python wechat_monitor.py 来启动微信监测程序
echo.
pause
